#include "plot/plot.h"

namespace trunk {
namespace pnd {
namespace plot {

Plot::Plot() {}
void Plot::initNum(const int& num) {
  num_ = num;
  Init();
  plt::clf();  // 清除之前画的图
}

void Plot::Init() {
  // plt::backend("TkAgg");
  plt::clf();         // 清除上一帧的数据
  plt::ion();         // 加这句为了动态画图
  plt::figure(num_);  // 初始化图片数量
}
void Plot::Show() {
  plt::legend();
  plt::pause(0.00000001);
  plt::show(false);  // 非阻塞绘图
  plt::show(false);
  plt::clf();
}

void Plot::plotData(const std::vector<double>& x, const std::vector<double>& y,
                    const std::string& color, const std::string& label) {
  std::unordered_map<std::string, std::string> keywords;
  keywords["color"] = color;
  keywords["label"] = label;
  // plt::subplot(2, 1, 1);
  plt::plot(x, y, keywords);
  plt::legend();
  plt::xlabel("x");
  plt::ylabel("y");
}

// void Plot::plotPath(const T& path_data, const std::string& color,
//                     const std::string& label) {
//   // plt::title(title);
//   std::vector<double> x, y;
//   for (const auto& point : path_data) {
//     x.push_back(point.s());
//     y.push_back(point.l());
//   }

//   std::map<std::string, std::string> keywords;
//   keywords["color"] = color;
//   keywords["label"] = "path";
//   plt::subplot(num_, 1, current_num_++);  // 绘制子图， 可以注释掉
//   plt::plot(x, y, keywords);
//   plt::legend();
//   plt::xlabel("s");
//   plt::ylabel("l");
// }

void Plot::plotDynameObs(const tport::Contour2D& obstacle_contour,
                         const std::string& color) {
  std::vector<double> x, y;
  for (const auto& point : obstacle_contour) {
    x.push_back(point.x());
    y.push_back(point.y());
  }
  std::unordered_map<std::string, std::string> keywords;
  keywords["color"] = color;
  keywords["label"] = "obs";
  // plt::subplot(2, 1, 1);
  plt::plot(x, y, keywords);
}

void Plot::plotSTBoundary(const port::STBoundary& st_boundary,
                          const std::string& color) {
  std::vector<double> x, y;
  for (const auto upper_point : st_boundary.upper_points()) {
    x.push_back(upper_point.t());
    y.push_back(upper_point.s());
  }
  for (int i = st_boundary.lower_points().size() - 1; i >= 0; --i) {
    x.push_back(st_boundary.lower_points()[i].t());
    y.push_back(st_boundary.lower_points()[i].s());
  }
  if (!x.empty() && !y.empty()) {
    x.push_back(x.front());
    y.push_back(y.front());
  }
  std::unordered_map<std::string, std::string> keywords;
  keywords["color"] = color;
  keywords["label"] = "S-T";
  // plt::subplot(num_, 1, 2);
  plt::plot(x, y, keywords);
#ifdef PLOT
  if (PyErr_Occurred()) {
    PyErr_Print();  // 打印 Python 端的错误
    return;
  }
#endif
}
}  // namespace plot
}  // namespace pnd
}  // namespace trunk
